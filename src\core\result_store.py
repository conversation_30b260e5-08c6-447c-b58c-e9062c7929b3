import logging
import json
import hashlib
import datetime
from typing import Dict, List, Any, Optional, Union
import numpy as np
import pandas as pd

from src.core.connection import ClickHouseConnection
from src.models.kpi import KPIType
from src.models.axis import Period
from src.utils.formatting import format_duration


class NumpyJSONEncoder(json.JSONEncoder):
    """JSON encoder that handles NumPy types and Pydantic models."""
    
    def default(self, obj):
        try:
            # Handle NumPy types
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.bool_):
                return bool(obj)
            
            # Handle Pydantic models
            if hasattr(obj, 'model_dump'):
                return obj.model_dump()
            elif hasattr(obj, 'dict'):
                return obj.dict()
            elif hasattr(obj, '__dict__'):
                return obj.__dict__
                
            return super().default(obj)
        except Exception as e:
            logging.error(f"JSON serialization error for {type(obj).__name__}: {e}")
            raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable") from e


class ResultStore:
    """
    ClickHouse-optimized result storage system for KPI analysis results.
    
    This implementation uses a unified partitioned table approach optimized for:
    - Multi-dimensional analytical queries
    - Efficient storage and compression
    - Clean Parquet export compatibility
    - High-performance time-series analysis
    """
    
    def __init__(self, connection: ClickHouseConnection):
        """
        Initialize ResultStore with ClickHouse connection.
        
        Args:
            connection: ClickHouse database connection
            
        Raises:
            ValueError: If connection is None
        """
        if not connection:
            raise ValueError("ClickHouse connection is required")
            
        self.connection = connection
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize storage schema
        self._initialize_storage()
    
    def _initialize_storage(self) -> None:
        """Initialize the optimized storage schema in ClickHouse."""
        try:
            # Create results database
            self.connection.execute_command("CREATE DATABASE IF NOT EXISTS results")
            
            # Create main results table with optimized schema
            self._create_main_results_table()
            
            # Create supporting tables
            self._create_job_tracking_table()
            self._create_access_log_table()
            
            self.logger.info("ClickHouse result storage initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize result storage: {e}")
            raise
    
    def _create_main_results_table(self) -> None:
        """Create the main results table with streamlined array-based schema for analytical queries."""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS results.kpi_results (
            -- Core identifiers (as specified in requirements)
            result_id String,
            job_id String,
            period_name String,
            retention_days UInt16 DEFAULT 90,

            -- Timestamp for partitioning and TTL
            created_at DateTime DEFAULT now(),

            -- Array-based dimensional data (replaces individual axis columns)
            -- Structure: Array of (axis_name, position_name, position_number)
            axes_data Array(Tuple(String, String, UInt32)) DEFAULT [],

            -- Array-based fact data (replaces individual fact columns)
            -- Structure: Array of (fact_name, fact_value, fact_id, fact_decimal_value)
            facts_data Array(Tuple(String, Float64, UInt32, Decimal64(4))) DEFAULT []
        )
        ENGINE = MergeTree()
        PARTITION BY toYYYYMM(created_at)
        ORDER BY (result_id, period_name, created_at)
        TTL created_at + INTERVAL retention_days DAY DELETE
        SETTINGS index_granularity = 8192
        """

        self.connection.execute_command(create_table_sql)

        # Add specialized indexes for common query patterns
        # Only create indexes for columns that actually exist in the streamlined schema
        indexes = [
            "ALTER TABLE results.kpi_results ADD INDEX idx_job_id job_id TYPE bloom_filter GRANULARITY 1",
            "ALTER TABLE results.kpi_results ADD INDEX idx_period period_name TYPE bloom_filter GRANULARITY 1"
        ]

        for index_sql in indexes:
            try:
                self.connection.execute_command(index_sql)
            except Exception as e:
                # Indexes might already exist or not supported, log but continue
                self.logger.debug(f"Index creation note: {e}")
    
    def _create_job_tracking_table(self) -> None:
        """Create job tracking table for job-level metadata."""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS results.job_metadata (
            job_id String,
            result_id String,
            analysis_name String,
            kpi_type String,
            id_panel UInt32,
            username String,
            
            -- Job timing
            job_started_at DateTime,
            job_completed_at DateTime DEFAULT toDateTime(0),
            job_duration_ms UInt64,
            
            -- Job configuration
            periods_count UInt32,
            axes_count UInt32,
            facts_count UInt32,
            
            -- Job results summary
            total_result_rows UInt64,
            job_status String,
            error_summary String DEFAULT '',
            
            -- Full job context (compressed)
            full_job_info String CODEC(ZSTD),
            
            created_at DateTime DEFAULT now()
        )
        ENGINE = ReplacingMergeTree(created_at)
        PARTITION BY toYYYYMM(job_started_at)
        ORDER BY (job_id, result_id)
        TTL job_started_at + INTERVAL 365 DAY DELETE
        """
        
        self.connection.execute_command(create_table_sql)
    
    def _create_access_log_table(self) -> None:
        """Create access log table for tracking result usage."""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS results.result_access (
            result_id String,
            access_time DateTime DEFAULT now(),
            user_id String,
            access_type Enum('view' = 1, 'export' = 2, 'query' = 3, 'delete' = 4),
            access_details String DEFAULT '',
            query_duration_ms UInt64 DEFAULT 0
        )
        ENGINE = MergeTree()
        PARTITION BY toYYYYMM(access_time)
        ORDER BY (result_id, access_time)
        TTL access_time + INTERVAL 180 DAY DELETE
        """
        
        self.connection.execute_command(create_table_sql)

    @staticmethod
    def escape_sql_string(value: Union[str, Any]) -> str:
        """
        Escape a string for safe use in SQL queries.

        Args:
            value: The value to escape (will be converted to string if not already)

        Returns:
            Escaped string safe for SQL insertion
        """
        if not isinstance(value, str):
            value = str(value)
        return value.replace("'", "''")

    def _generate_result_id(self, job_id: str, analysis_name: str, period_name: str, kpi_type: str) -> str:
        """
        Generate a unique result ID based on job parameters.

        Args:
            job_id: Job identifier
            analysis_name: Analysis name
            period_name: Period name
            kpi_type: KPI type

        Returns:
            Unique result ID
        """
        # Create a hash of the key parameters
        hash_input = f"{job_id}_{analysis_name}_{period_name}_{kpi_type}_{datetime.datetime.now().isoformat()}"
        result_hash = hashlib.md5(hash_input.encode()).hexdigest()[:12]

        # Create readable result ID
        safe_analysis = analysis_name.replace(" ", "_").replace("-", "_")[:20]
        safe_period = period_name.replace(" ", "_").replace("-", "_")[:15]

        return f"{safe_analysis}_{safe_period}_{kpi_type}_{result_hash}"

    def store_result_direct(
        self,
        query_text: str,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        kpi_type: str,
        id_panel: int,
        axes: Dict[str, Any],
        filters: Dict[str, Any],
        combined_result_id: Optional[str] = None,
        query_steps: Optional[str] = None,
        query_ids: Optional[List[str]] = None,
        settings: Optional[Dict[str, Any]] = None,
        username: str = "",
        su_fact_name: Optional[str] = None,
        job_duration: Optional[float] = None,
        error_info: Optional[Dict[str, Any]] = None,
        job_status: str = "in_progress",
    ) -> str:
        """
        Store KPI analysis results directly in the optimized ClickHouse schema.

        This method transforms the query results into the new dimensional format
        and stores them in the kpi_results table with proper normalization.

        Args:
            query_text: The original query text
            job_id: Job ID for tracking
            analysis_name: Analysis name
            periods: List of Period models for all periods in the job
            kpi_type: Type of KPI (standard_kpi or catman_kpi)
            id_panel: Panel ID
            axes: Dictionary of axes configuration
            filters: Dictionary of filters configuration
            combined_result_id: Optional ID for combining multiple periods
            query_steps: Optional query execution steps
            query_ids: Optional list of query IDs
            settings: Optional ClickHouse settings
            username: Username for the job
            su_fact_name: Name of the SU fact used
            job_duration: Job duration in milliseconds
            error_info: Optional error information
            job_status: Job status

        Returns:
            Result ID
        """
        try:
            # Validate periods parameter
            if not isinstance(periods, list) or not periods:
                raise ValueError("'periods' must be a non-empty list of Period objects")

            # Generate result ID
            if combined_result_id:
                result_id = combined_result_id
            else:
                combined_period_name = "_".join([p.label for p in periods])
                result_id = self._generate_result_id(job_id, analysis_name, combined_period_name, kpi_type)

            # Record start time
            job_start_time = datetime.datetime.now()

            # Handle error cases
            if error_info:
                self.logger.info(f"Storing error result for {result_id}")
                self._store_error_result(
                    result_id, job_id, analysis_name, periods, kpi_type, id_panel,
                    axes, filters, query_steps, query_ids, username, job_duration,
                    error_info, job_start_time
                )
                return result_id

            # Execute query and transform results
            self.logger.info(f"Executing and storing results for {result_id}")
            result_rows = self._execute_and_store_query(
                query_text, result_id, job_id, analysis_name, periods, kpi_type,
                id_panel, axes, filters, query_steps, query_ids, settings,
                username, su_fact_name, job_duration, job_start_time
            )

            # Store job metadata for successful case
            try:
                self._store_job_metadata_for_error(
                    job_id, result_id, analysis_name, kpi_type, id_panel, username,
                    periods, axes, filters, job_start_time, job_duration, result_rows, "done"
                )
            except Exception as meta_error:
                self.logger.error(f"Failed to store job metadata for success: {meta_error}")

            self.logger.info(f"Successfully stored result {result_id} with {result_rows} rows")
            return result_id

        except Exception as e:
            self.logger.error(f"Failed to store result: {e}")
            # Store error information if we have enough context
            if 'result_id' in locals():
                try:
                    error_dict = {"message": str(e), "error_type": type(e).__name__}
                    self._store_error_result(
                        result_id, job_id, analysis_name, periods, kpi_type, id_panel,
                        axes, filters, query_steps, query_ids, username, job_duration,
                        error_dict, datetime.datetime.now()
                    )
                except:
                    pass  # Don't mask original error
            raise

    def _execute_and_store_query(
        self,
        query_text: str,
        result_id: str,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        kpi_type: str,
        id_panel: int,
        axes: Dict[str, Any],
        filters: Dict[str, Any],
        query_steps: Optional[str],
        query_ids: Optional[List[str]],
        settings: Optional[Dict[str, Any]],
        username: str,
        su_fact_name: Optional[str],
        job_duration: Optional[float],
        job_start_time: datetime.datetime,
    ) -> int:
        """
        Execute the query and store results in the optimized format.

        This method transforms the original query results into the new dimensional
        schema with proper normalization of axes, periods, and facts.

        Returns:
            Number of rows stored
        """
        try:
            # Execute the original query to get raw results
            self.logger.info(f"Executing query for result {result_id}")
            # Note: get_query_dataframe doesn't support settings parameter
            raw_results = self.connection.get_query_dataframe(query_text)

            if raw_results.empty:
                self.logger.warning(f"Query returned no results for {result_id}")
                return 0

            # Transform and insert results
            total_rows = self._transform_and_insert_results(
                raw_results, result_id, job_id, analysis_name, periods, kpi_type,
                id_panel, axes, filters, query_steps, query_ids, username,
                su_fact_name, job_duration, job_start_time
            )

            return total_rows

        except Exception as e:
            self.logger.error(f"Failed to execute and store query for {result_id}: {e}")
            raise

    def _transform_and_insert_results(
        self,
        raw_results,
        result_id: str,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        kpi_type: str,
        id_panel: int,
        axes: Dict[str, Any],
        filters: Dict[str, Any],
        query_steps: Optional[str],
        query_ids: Optional[List[str]],
        username: str,
        su_fact_name: Optional[str],
        job_duration: Optional[float],
        job_start_time: datetime.datetime,
    ) -> int:
        """
        Transform raw query results into the streamlined array-based schema format.

        This method normalizes the data by:
        1. Extracting dimension columns (those ending with _position_number and _name)
        2. Extracting fact columns (numeric measures)
        3. Creating arrays for axes and facts data
        4. Creating one row per period with all axes and facts as arrays

        Returns:
            Number of rows inserted
        """
        try:
            # Analyze the result structure
            dimension_info = self._extract_dimension_info(raw_results)
            fact_columns = self._extract_fact_columns(raw_results, dimension_info)

            # Transform each row of raw results into array-based format
            normalized_rows = []

            for _, row in raw_results.iterrows():
                # Extract period information
                period_name = row.get('period_name', periods[0].label if periods else 'unknown')

                # Extract axes data as array of tuples
                axes_data = self._extract_axes_array(row, dimension_info)

                # Extract facts data as array of tuples
                facts_data = self._extract_facts_array(row, fact_columns)

                normalized_row = {
                    'result_id': result_id,
                    'job_id': job_id,
                    'period_name': period_name,
                    'retention_days': 90,  # Default retention
                    'created_at': job_start_time,
                    'axes_data': axes_data,
                    'facts_data': facts_data
                }

                normalized_rows.append(normalized_row)

            # Batch insert the normalized rows
            if normalized_rows:
                self._batch_insert_results(normalized_rows)
                self.logger.info(f"Inserted {len(normalized_rows)} streamlined rows for {result_id}")

            return len(normalized_rows)

        except Exception as e:
            self.logger.error(f"Failed to transform and insert results: {e}")
            raise

    def _extract_dimension_info(self, raw_results) -> Dict[str, str]:
        """
        Extract dimension information from raw results columns.

        Identifies dimension columns by looking for patterns like:
        - {dimension_name}_position_number
        - {dimension_name}_name or {dimension_name}_position_name

        Returns:
            Dictionary mapping dimension names to their column patterns
        """
        dimension_info = {}
        columns = raw_results.columns.tolist()

        # Find position_number columns (these define our dimensions)
        for col in columns:
            if col.endswith('_position_number'):
                dim_name = col.replace('_position_number', '')

                # Look for corresponding name column
                name_col = None
                for name_pattern in [f'{dim_name}_name', f'{dim_name}_position_name']:
                    if name_pattern in columns:
                        name_col = name_pattern
                        break

                dimension_info[dim_name] = {
                    'position_col': col,
                    'name_col': name_col or col  # Fallback to position column
                }

        return dimension_info

    def _extract_fact_columns(self, raw_results, dimension_info: Dict[str, str]) -> List[str]:
        """
        Extract fact (measure) columns from raw results.

        Fact columns are numeric columns that are not:
        - Dimension position or name columns
        - Period columns
        - System columns

        Returns:
            List of fact column names
        """
        columns = raw_results.columns.tolist()

        # Columns to exclude from facts
        excluded_columns = set()

        # Add dimension columns
        for dim_info in dimension_info.values():
            excluded_columns.add(dim_info['position_col'])
            if dim_info['name_col']:
                excluded_columns.add(dim_info['name_col'])

        # Add system columns
        excluded_columns.update(['period_name', 'period_start', 'period_end'])

        # Find numeric columns that aren't excluded
        fact_columns = []
        for col in columns:
            if col not in excluded_columns:
                # Check if column contains numeric data
                try:
                    if raw_results[col].dtype in ['int64', 'float64', 'int32', 'float32']:
                        fact_columns.append(col)
                except:
                    pass  # Skip columns that can't be checked

        return fact_columns

    def _extract_axes_array(self, row, dimension_info: Dict[str, str]) -> List[tuple]:
        """
        Extract axes data as an array of tuples for the new schema.

        Returns:
            List of tuples (axis_name, position_name, position_number)
        """
        axes_data = []

        for dim_name, dim_info in dimension_info.items():
            position_col = dim_info['position_col']
            name_col = dim_info['name_col']

            axis_name = str(dim_name)
            position_number = int(row.get(position_col, 0))
            position_name = str(row.get(name_col, ''))

            # Only add non-empty axes
            if position_number > 0 or position_name:
                # Ensure all values are properly typed for ClickHouse
                axes_data.append((axis_name, position_name, position_number))

        # If no axes found, add a default entry to avoid empty arrays
        if not axes_data:
            axes_data.append(('default', 'Total', 1))

        return axes_data

    def _extract_facts_array(self, row, fact_columns: List[str]) -> List[tuple]:
        """
        Extract facts data as an array of tuples for the new schema.

        Returns:
            List of tuples (fact_name, fact_value, fact_id, fact_decimal_value)
        """
        facts_data = []

        for fact_name in fact_columns:
            fact_value = float(row.get(fact_name, 0.0))
            fact_id = 0  # Default fact ID, could be enhanced to map actual IDs

            # Convert to Decimal for ClickHouse Decimal64(4) type
            from decimal import Decimal, ROUND_HALF_UP
            fact_decimal_value = Decimal(str(fact_value)).quantize(
                Decimal('0.0001'), rounding=ROUND_HALF_UP
            )

            # Ensure all values are properly typed for ClickHouse
            facts_data.append((str(fact_name), fact_value, fact_id, fact_decimal_value))

        # If no facts found, add a default entry to avoid empty arrays
        if not facts_data:
            facts_data.append(('total', 0.0, 0, Decimal('0.0000')))

        return facts_data





    def _batch_insert_results(self, normalized_rows: List[Dict[str, Any]]) -> None:
        """
        Batch insert normalized results into the streamlined kpi_results table.

        Uses ClickHouse's efficient batch insert capabilities with array data types.
        """
        if not normalized_rows:
            return

        try:
            # Prepare data for batch insert with the new schema
            batch_data = []
            for row in normalized_rows:
                row_values = [
                    row['result_id'],
                    row['job_id'],
                    row['period_name'],
                    row['retention_days'],
                    row['created_at'],
                    row['axes_data'],  # Array of tuples
                    row['facts_data']  # Array of tuples
                ]
                batch_data.append(row_values)

            # Column names for the new schema
            column_names = ['result_id', 'job_id', 'period_name', 'retention_days', 'created_at', 'axes_data', 'facts_data']

            # Execute batch insert
            self.connection.client.insert(
                'results.kpi_results',
                batch_data,
                column_names=column_names
            )

            self.logger.info(f"Successfully batch inserted {len(batch_data)} streamlined rows")

        except Exception as e:
            self.logger.error(f"Failed to batch insert results: {e}")
            raise

    def _store_error_result(
        self,
        result_id: str,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        kpi_type: str,
        id_panel: int,
        axes: Dict[str, Any],
        filters: Dict[str, Any],
        query_steps: Optional[str],
        query_ids: Optional[List[str]],
        username: str,
        job_duration: Optional[float],
        error_info: Dict[str, Any],
        job_start_time: datetime.datetime,
    ) -> None:
        """
        Store error information when a job fails.

        Creates a minimal record in the results table with error details.
        """
        try:
            # Create error record for each period
            error_rows = []
            for period in periods:
                # Create empty arrays for error case
                axes_data = [('ERROR', 'Error occurred', 0)]
                facts_data = [('ERROR', 0.0, 0, 0.0)]

                error_row = {
                    'result_id': result_id,
                    'job_id': job_id,
                    'period_name': period.label,
                    'retention_days': 90,
                    'created_at': job_start_time,
                    'axes_data': axes_data,
                    'facts_data': facts_data
                }
                error_rows.append(error_row)

            # Insert error records
            if error_rows:
                self._batch_insert_results(error_rows)
                self.logger.info(f"Stored error result {result_id} with {len(error_rows)} error records")

            # Store job metadata for error case
            try:
                self._store_job_metadata_for_error(
                    job_id, result_id, analysis_name, kpi_type, id_panel, username,
                    periods, axes, filters, job_start_time, job_duration, 0, "error",
                    error_info.get('message', 'Unknown error')
                )
            except Exception as meta_error:
                self.logger.error(f"Failed to store job metadata for error: {meta_error}")

        except Exception as e:
            self.logger.error(f"Failed to store error result: {e}")
            # Don't re-raise to avoid masking original error



    def _store_job_metadata_for_error(
        self,
        job_id: str,
        result_id: str,
        analysis_name: str,
        kpi_type: str,
        id_panel: int,
        username: str,
        periods: List[Period],
        axes: Dict[str, Any],
        filters: Dict[str, Any],
        job_start_time: datetime.datetime,
        job_duration: Optional[float],
        result_rows: int,
        job_status: str,
        error_summary: str = "",
    ) -> None:
        """
        Store job-level metadata for error cases.
        """
        try:
            # Prepare full job info
            full_job_info = {
                'result_id': result_id,
                'analysis_name': analysis_name,
                'kpi_type': kpi_type,
                'periods': [
                    {
                        'name': p.label,
                        'start_date': p.date_start.isoformat() if hasattr(p.date_start, 'isoformat') else str(p.date_start),
                        'end_date': p.date_end.isoformat() if hasattr(p.date_end, 'isoformat') else str(p.date_end)
                    } for p in periods
                ],
                'axes_summary': {k: str(v)[:100] for k, v in axes.items()},
                'filters_summary': {k: str(v)[:100] for k, v in filters.items()},
                'execution_timestamp': job_start_time.isoformat(),
                'job_duration_ms': job_duration or 0,
                'result_rows': result_rows,
                'job_status': job_status,
                'error_summary': error_summary
            }

            # Insert job metadata
            job_completed_time = datetime.datetime.now()

            insert_data = {
                'job_id': job_id,
                'result_id': result_id,
                'analysis_name': analysis_name,
                'kpi_type': kpi_type,
                'id_panel': id_panel,
                'username': username,
                'job_started_at': job_start_time,
                'job_completed_at': job_completed_time,
                'job_duration_ms': int(job_duration or 0),
                'periods_count': len(periods),
                'axes_count': len(axes),
                'facts_count': result_rows,
                'total_result_rows': result_rows,
                'job_status': job_status,
                'error_summary': error_summary,
                'full_job_info': json.dumps(full_job_info, cls=NumpyJSONEncoder),
                'created_at': job_completed_time
            }

            # Use ClickHouse client insert
            self.connection.client.insert(
                'results.job_metadata',
                [list(insert_data.values())],
                column_names=list(insert_data.keys())
            )

            self.logger.info(f"Stored error job metadata for {job_id}")

        except Exception as e:
            self.logger.error(f"Failed to store error job metadata: {e}")
            # Don't re-raise as this is supplementary data

    def create_or_update_job_record(
        self,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        kpi_type: str,
        id_panel: int,
        axes: Dict[str, Any],
        filters: Dict[str, Any],
        username: str,
        job_status: str = "in_progress"
    ) -> None:
        """
        Create or update a job record in the job_metadata table.

        Args:
            job_id: Job identifier
            analysis_name: Analysis name
            periods: List of periods
            kpi_type: KPI type
            id_panel: Panel ID
            axes: Axes configuration
            filters: Filters configuration
            username: Username
            job_status: Job status
        """
        try:
            job_start_time = datetime.datetime.now()

            # Prepare job info
            full_job_info = {
                'analysis_name': analysis_name,
                'kpi_type': kpi_type,
                'periods': [
                    {
                        'name': p.label,
                        'start_date': p.date_start.isoformat() if hasattr(p.date_start, 'isoformat') else str(p.date_start),
                        'end_date': p.date_end.isoformat() if hasattr(p.date_end, 'isoformat') else str(p.date_end)
                    } for p in periods
                ],
                'axes_summary': {k: str(v)[:100] for k, v in axes.items()},
                'filters_summary': {k: str(v)[:100] for k, v in filters.items()},
                'execution_timestamp': job_start_time.isoformat(),
                'job_status': job_status
            }

            insert_data = {
                'job_id': job_id,
                'result_id': job_id,  # Use job_id as result_id for initial record
                'analysis_name': analysis_name,
                'kpi_type': kpi_type,
                'id_panel': id_panel,
                'username': username,
                'job_started_at': job_start_time,
                'job_completed_at': datetime.datetime.fromtimestamp(0),  # Will be updated later
                'job_duration_ms': 0,  # Will be updated later
                'periods_count': len(periods),
                'axes_count': len(axes),
                'facts_count': 0,  # Will be updated later
                'total_result_rows': 0,  # Will be updated later
                'job_status': job_status,
                'error_summary': '',
                'full_job_info': json.dumps(full_job_info, cls=NumpyJSONEncoder),
                'created_at': job_start_time
            }

            # Use ClickHouse client insert
            self.connection.client.insert(
                'results.job_metadata',
                [list(insert_data.values())],
                column_names=list(insert_data.keys())
            )

            self.logger.info(f"Created job record for {job_id}")

        except Exception as e:
            self.logger.error(f"Failed to create job record: {e}")
            # Don't re-raise as this is supplementary data

    def update_job_status(self, result_id: str, status: str) -> None:
        """
        Update job status in the job_metadata table.

        Args:
            result_id: Result/Job ID
            status: New status
        """
        try:
            update_query = f"""
            ALTER TABLE results.job_metadata
            UPDATE job_status = '{self.escape_sql_string(status)}'
            WHERE job_id = '{self.escape_sql_string(result_id)}'
            """

            self.connection.execute_command(update_query)
            self.logger.info(f"Updated job status to '{status}' for {result_id}")

        except Exception as e:
            self.logger.error(f"Failed to update job status: {e}")

    def update_job_status_and_duration(
        self,
        result_id: str,
        status: str,
        duration_ms: float
    ) -> None:
        """
        Update job status and duration in the job_metadata table.

        Args:
            result_id: Result/Job ID
            status: New status
            duration_ms: Job duration in milliseconds
        """
        try:
            completed_time = datetime.datetime.now()

            update_query = f"""
            ALTER TABLE results.job_metadata
            UPDATE
                job_status = '{self.escape_sql_string(status)}',
                job_duration_ms = {int(duration_ms)},
                job_completed_at = '{completed_time.strftime('%Y-%m-%d %H:%M:%S')}'
            WHERE job_id = '{self.escape_sql_string(result_id)}'
            """

            self.connection.execute_command(update_query)
            self.logger.info(f"Updated job status to '{status}' and duration to {duration_ms}ms for {result_id}")

        except Exception as e:
            self.logger.error(f"Failed to update job status and duration: {e}")

    def get_result_summary(self, result_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a summary of a stored result.

        Args:
            result_id: Result ID to query

        Returns:
            Dictionary with result summary or None if not found
        """
        try:
            query = f"""
            SELECT
                result_id,
                job_id,
                count() as total_rows,
                countDistinct(period_name) as periods_count,
                sum(length(facts_data)) as facts_count,
                sum(length(axes_data)) as axes_count,
                any(retention_days) as retention_days
            FROM results.kpi_results
            WHERE result_id = '{self.escape_sql_string(result_id)}'
            GROUP BY result_id, job_id
            """

            result = self.connection.get_query_dataframe(query)

            if result.empty:
                return None

            return result.iloc[0].to_dict()

        except Exception as e:
            self.logger.error(f"Failed to get result summary for {result_id}: {e}")
            return None

    def get_result_data(
        self,
        result_id: str,
        period_name: Optional[str] = None,
        fact_names: Optional[List[str]] = None,
        limit: int = 1000
    ) -> Optional[Dict[str, Any]]:
        """
        Get result data in a structured format suitable for analysis or export.

        Args:
            result_id: Result ID to query
            period_name: Optional period filter
            fact_names: Optional list of facts to include
            limit: Maximum number of rows to return

        Returns:
            Dictionary with structured result data
        """
        try:
            # Build query with optional filters
            where_conditions = [f"result_id = '{self.escape_sql_string(result_id)}'"]

            if period_name:
                where_conditions.append(f"period_name = '{self.escape_sql_string(period_name)}'")

            where_clause = " AND ".join(where_conditions)

            query = f"""
            SELECT
                result_id,
                job_id,
                period_name,
                retention_days,
                created_at,
                axes_data,
                facts_data
            FROM results.kpi_results
            WHERE {where_clause}
            ORDER BY created_at, period_name
            LIMIT {limit}
            """

            result_data = self.connection.get_query_dataframe(query)

            if result_data.empty:
                return None

            # Transform array data for easier consumption
            structured_data = []
            for _, row in result_data.iterrows():
                # Parse axes data
                axes_info = []
                for axis_tuple in row['axes_data']:
                    axes_info.append({
                        'axis_name': axis_tuple[0],
                        'position_name': axis_tuple[1],
                        'position_number': axis_tuple[2]
                    })

                # Parse facts data
                facts_info = []
                for fact_tuple in row['facts_data']:
                    if not fact_names or fact_tuple[0] in fact_names:
                        facts_info.append({
                            'fact_name': fact_tuple[0],
                            'fact_value': fact_tuple[1],
                            'fact_id': fact_tuple[2],
                            'fact_decimal_value': fact_tuple[3]
                        })

                structured_data.append({
                    'result_id': row['result_id'],
                    'job_id': row['job_id'],
                    'period_name': row['period_name'],
                    'retention_days': row['retention_days'],
                    'created_at': row['created_at'],
                    'axes': axes_info,
                    'facts': facts_info
                })

            # Extract unique periods and facts for summary
            all_periods = list(set(row['period_name'] for row in structured_data))
            all_facts = []
            for row in structured_data:
                all_facts.extend([fact['fact_name'] for fact in row['facts']])
            unique_facts = list(set(all_facts))

            return {
                'result_id': result_id,
                'total_rows': len(structured_data),
                'periods': all_periods,
                'facts': unique_facts,
                'data': structured_data
            }

        except Exception as e:
            self.logger.error(f"Failed to get result data for {result_id}: {e}")
            return None

    def delete_result(self, result_id: str, user_id: str = "") -> bool:
        """
        Delete a result and all associated data.

        Args:
            result_id: Result ID to delete
            user_id: User performing the deletion

        Returns:
            True if successful, False otherwise
        """
        try:
            # Log the access
            self._log_result_access(result_id, user_id, 'delete')

            # Delete from main results table
            delete_query = f"ALTER TABLE results.kpi_results DELETE WHERE result_id = '{self.escape_sql_string(result_id)}'"
            self.connection.execute_command(delete_query)

            # Delete from job metadata
            delete_job_query = f"ALTER TABLE results.job_metadata DELETE WHERE result_id = '{self.escape_sql_string(result_id)}'"
            self.connection.execute_command(delete_job_query)

            self.logger.info(f"Deleted result {result_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete result {result_id}: {e}")
            return False

    def _log_result_access(
        self,
        result_id: str,
        user_id: str,
        access_type: str,
        details: str = "",
        duration_ms: int = 0
    ) -> None:
        """
        Log result access for auditing and usage tracking.
        """
        try:
            access_data = {
                'result_id': result_id,
                'access_time': datetime.datetime.now(),
                'user_id': user_id,
                'access_type': access_type,
                'access_details': details,
                'query_duration_ms': duration_ms
            }

            self.connection.client.insert(
                'results.result_access',
                [list(access_data.values())],
                column_names=list(access_data.keys())
            )

        except Exception as e:
            self.logger.debug(f"Failed to log result access: {e}")
            # Don't raise as this is non-critical

    def cleanup_old_results(self, days_old: int = 90) -> int:
        """
        Clean up results older than specified days.

        Args:
            days_old: Number of days after which to delete results

        Returns:
            Number of results cleaned up
        """
        try:
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_old)

            # Count results to be deleted
            count_query = f"""
            SELECT count(DISTINCT result_id) as count
            FROM results.kpi_results
            WHERE created_at < '{cutoff_date.strftime('%Y-%m-%d %H:%M:%S')}'
            """
            count_result = self.connection.get_query_dataframe(count_query)
            results_to_delete = count_result.iloc[0]['count'] if not count_result.empty else 0

            if results_to_delete > 0:
                # Delete old results
                cutoff_str = cutoff_date.strftime('%Y-%m-%d %H:%M:%S')
                delete_query = f"ALTER TABLE results.kpi_results DELETE WHERE created_at < '{cutoff_str}'"
                self.connection.execute_command(delete_query)

                # Delete old job metadata
                delete_job_query = f"ALTER TABLE results.job_metadata DELETE WHERE job_started_at < '{cutoff_str}'"
                self.connection.execute_command(delete_job_query)

                self.logger.info(f"Cleaned up {results_to_delete} old results")

            return results_to_delete

        except Exception as e:
            self.logger.error(f"Failed to cleanup old results: {e}")
            return 0

    def create_result_table_for_all_periods(
        self,
        result_id: str,
        job_info: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Compatibility method for existing codebase.

        In the new streamlined architecture, results are already stored in the optimized format,
        so this method just validates the result exists and returns status.

        Args:
            result_id: Result ID to check
            job_info: Optional job information (not used in new architecture)

        Returns:
            Dictionary with status information
        """
        try:
            # Check if result exists
            summary = self.get_result_summary(result_id)

            if not summary:
                return {
                    "created": False,
                    "reason": f"Result {result_id} not found",
                    "table_name": "",
                    "row_count": 0,
                }

            # Success case - no error checking needed in streamlined schema
            return {
                "created": True,
                "reason": "",
                "table_name": f"kpi_results (filtered by result_id = {result_id})",
                "row_count": summary.get('total_rows', 0),
            }

        except Exception as e:
            error_msg = f"Failed to validate result table: {e}"
            self.logger.error(error_msg)
            return {
                "created": False,
                "reason": error_msg,
                "table_name": "",
                "row_count": 0,
            }

    def export_to_parquet(
        self,
        result_id: str,
        output_path: str,
        period_name: Optional[str] = None,
        fact_names: Optional[List[str]] = None
    ) -> bool:
        """
        Export result data to Parquet format for external analytics.

        The optimized schema is designed to be Parquet-friendly with:
        - Clean columnar structure
        - Proper data types
        - Minimal nesting

        Args:
            result_id: Result ID to export
            output_path: Path for the output Parquet file
            period_name: Optional period filter
            fact_names: Optional list of facts to include

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get result data
            result_data = self.get_result_data(
                result_id, period_name, fact_names, limit=1000000  # Large limit for export
            )

            if not result_data:
                self.logger.error(f"No data found for result {result_id}")
                return False

            # Convert to DataFrame for Parquet export
            df = pd.DataFrame(result_data['data'])

            # Optimize data types for Parquet
            df = self._optimize_dataframe_for_parquet(df)

            # Export to Parquet
            df.to_parquet(output_path, index=False, compression='snappy')

            self.logger.info(f"Exported {len(df)} rows to {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to export to Parquet: {e}")
            return False

    def _optimize_dataframe_for_parquet(self, df):
        """
        Optimize DataFrame data types for efficient Parquet storage with the new schema.
        """
        try:
            # Optimize string columns (use category for repeated values)
            string_cols = ['result_id', 'job_id', 'period_name']
            for col in string_cols:
                if col in df.columns and df[col].nunique() < len(df) * 0.5:
                    df[col] = df[col].astype('category')

            # Optimize integer columns
            if 'retention_days' in df.columns:
                df['retention_days'] = df['retention_days'].astype('int16')

            return df

        except Exception as e:
            self.logger.warning(f"Failed to optimize DataFrame for Parquet: {e}")
            return df  # Return original if optimization fails
