# ClickHouse kpi_results Table Schema Streamlining

## Overview

This document summarizes the changes made to streamline the ClickHouse `kpi_results` table schema according to the specified requirements.

## Changes Made

### 1. Table Schema Modification

**Before (Old Schema):**
- 20+ columns including individual axis columns (axis1_name, axis1_position_name, axis1_position_number, etc.)
- Individual fact columns (fact_name, fact_value)
- Multiple metadata columns with JSON configurations
- Complex partitioning and ordering

**After (New Streamlined Schema):**
```sql
CREATE TABLE results.kpi_results (
    -- Core identifiers (as specified in requirements)
    result_id String,
    job_id String,
    period_name String,
    retention_days UInt16 DEFAULT 90,
    
    -- Array-based dimensional data
    axes_data Array(Tuple(String, String, UInt32)) DEFAULT [],
    
    -- Array-based fact data  
    facts_data Array(Tuple(String, Float64, UInt32, Decimal64(4))) DEFAULT []
)
```

### 2. Key Benefits

**Streamlined Structure:**
- Reduced from 20+ columns to just 6 core columns
- Maintains all required functionality
- Supports 1-4 axes with position names/numbers
- Supports fact collections with name/value pairs, IDs, and decimal values
- Multiple periods per result through separate rows

**Array-Based Storage:**
- `axes_data`: Array of (axis_name, position_name, position_number)
- `facts_data`: Array of (fact_name, fact_value, fact_id, fact_decimal_value)
- Optimized for ClickHouse analytical queries
- Clean Parquet export without overly nested structures

### 3. Code Changes

#### Modified Files:

**src/core/result_store.py:**
- Updated `_create_main_results_table()` with new schema
- Modified `_transform_and_insert_results()` for array-based data
- Added `_extract_axes_array()` and `_extract_facts_array()` helper methods
- Updated `_batch_insert_results()` for new column structure
- Modified `get_result_summary()` and `get_result_data()` for array queries
- Streamlined `_store_error_result()` for new schema
- Updated `_optimize_dataframe_for_parquet()` for new structure
- Removed obsolete metadata preparation methods

**docs/result_store_architecture.md:**
- Updated schema documentation
- Added array structure details
- Documented benefits of streamlined approach

### 4. Backward Compatibility

**Maintained Methods:**
- `store_result_direct()` - Core storage functionality preserved
- `get_result_summary()` - Returns summary with new structure
- `get_result_data()` - Returns structured data with arrays parsed
- `export_to_parquet()` - Works with new optimized structure
- `create_result_table_for_all_periods()` - Compatibility method maintained

**Data Structure Changes:**
- Raw data is now stored in arrays instead of individual columns
- Query results are transformed to maintain API compatibility
- Export functionality optimized for new structure

### 5. Performance Improvements

**Storage Efficiency:**
- Reduced column count improves compression
- Array storage is more efficient for variable-length data
- Simplified partitioning and ordering

**Query Performance:**
- Optimized for analytical workloads
- Better compression ratios
- Cleaner structure for aggregations

**Export Performance:**
- Parquet-friendly structure
- Reduced nesting complexity
- Optimized data types

### 6. Migration Considerations

**Automatic Migration:**
- New schema is created automatically when ResultStore is initialized
- Old data would need manual migration if preserving existing results
- New data uses streamlined format immediately

**Testing:**
- Created `test_streamlined_schema.py` for validation
- Tests schema creation, data insertion, retrieval, and export
- Verifies array structure integrity

### 7. Array Structure Details

**Axes Data Array:**
```python
# Structure: Array(Tuple(String, String, UInt32))
# Example: [('brand', 'Brand A', 1), ('category', 'Category X', 2)]
axes_data = [
    (axis_name, position_name, position_number),
    ...
]
```

**Facts Data Array:**
```python
# Structure: Array(Tuple(String, Float64, UInt32, Decimal64(4)))
# Example: [('revenue', 1000.50, 55, 1000.5000), ('units', 150.0, 60, 150.0000)]
facts_data = [
    (fact_name, fact_value, fact_id, fact_decimal_value),
    ...
]
```

### 8. Usage Examples

**Storing Results:**
```python
result_store = ResultStore(connection)
result_id = result_store.store_result_direct(
    query_text=kpi_query,
    job_id="job_123", 
    analysis_name="Monthly Sales Analysis",
    periods=[period1, period2],
    kpi_type="standard_kpi",
    id_panel=1,
    axes=axes_config,
    filters=filters_config,
    username="analyst"
)
```

**Querying Results:**
```python
# Get summary
summary = result_store.get_result_summary(result_id)

# Get structured data with parsed arrays
data = result_store.get_result_data(result_id)
for row in data['data']:
    print(f"Period: {row['period_name']}")
    print(f"Axes: {row['axes']}")  # Parsed array as list of dicts
    print(f"Facts: {row['facts']}")  # Parsed array as list of dicts
```

### 9. Validation

**Test Coverage:**
- Schema creation validation
- Data insertion with arrays
- Data retrieval and parsing
- Export functionality
- Error handling

**Run Tests:**
```bash
python test_streamlined_schema.py
```

## Conclusion

The streamlined schema successfully meets all requirements:
- ✅ Keeps only specified core columns (result_id, job_id, period_name, retention_days)
- ✅ Replaces axes structure with optimized arrays supporting position names/numbers
- ✅ Replaces facts structure with arrays supporting name/value pairs, IDs, and decimals
- ✅ Maintains support for multiple periods per result
- ✅ Optimized for ClickHouse analytical queries
- ✅ Clean Parquet export capability without overly nested structures
- ✅ Backward compatibility maintained through API layer

The new schema provides significant improvements in storage efficiency, query performance, and maintainability while preserving all required functionality.
