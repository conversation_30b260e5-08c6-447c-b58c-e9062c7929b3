#!/usr/bin/env python3
"""
Test script for the streamlined ClickHouse kpi_results table schema.

This script tests the new array-based schema modifications to ensure:
1. Table creation works with the new schema
2. Data insertion works with arrays
3. Data retrieval works correctly
4. Export functionality works
"""

import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any, List

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.connection import ClickHouseConnection
from src.core.result_store import ResultStore
from src.models.axis import Period

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data() -> Dict[str, Any]:
    """Create test data for the streamlined schema."""
    
    # Create test periods
    periods = [
        Period(
            label="2024-01",
            date_start="2024-01-01",
            date_end="2024-01-31"
        ),
        Period(
            label="2024-02", 
            date_start="2024-02-01",
            date_end="2024-02-29"
        )
    ]
    
    # Create test axes configuration
    axes = {
        "brand": {
            "axis_id": 1,
            "name": "Brand",
            "type": "standard"
        },
        "category": {
            "axis_id": 2,
            "name": "Category", 
            "type": "standard"
        }
    }
    
    # Create test filters
    filters = {
        "region": {
            "name": "Region",
            "type": "filter"
        }
    }
    
    return {
        "periods": periods,
        "axes": axes,
        "filters": filters,
        "query_text": """
        SELECT 
            '2024-01' as period_name,
            'Brand A' as brand_name,
            1 as brand_position_number,
            'Category X' as category_name, 
            2 as category_position_number,
            1000.50 as revenue,
            150 as units,
            75.25 as profit
        UNION ALL
        SELECT
            '2024-02' as period_name,
            'Brand B' as brand_name,
            2 as brand_position_number,
            'Category Y' as category_name,
            3 as category_position_number, 
            2000.75 as revenue,
            300 as units,
            150.50 as profit
        """
    }

def test_schema_creation(result_store: ResultStore) -> bool:
    """Test that the new schema can be created successfully."""
    try:
        logger.info("Testing schema creation...")
        
        # The schema should already be created during ResultStore initialization
        # Let's verify by checking if we can query the table structure
        query = """
        DESCRIBE TABLE results.kpi_results
        """
        
        result = result_store.connection.get_query_dataframe(query)
        logger.info(f"Table structure: {result.to_dict('records')}")
        
        # Verify expected columns exist
        expected_columns = ['result_id', 'job_id', 'period_name', 'retention_days', 'axes_data', 'facts_data']
        actual_columns = result['name'].tolist()
        
        for col in expected_columns:
            if col not in actual_columns:
                logger.error(f"Missing expected column: {col}")
                return False
                
        logger.info("✓ Schema creation test passed")
        return True
        
    except Exception as e:
        logger.error(f"Schema creation test failed: {e}")
        return False

def test_data_insertion(result_store: ResultStore, test_data: Dict[str, Any]) -> str:
    """Test data insertion with the new schema."""
    try:
        logger.info("Testing data insertion...")
        
        result_id = result_store.store_result_direct(
            query_text=test_data["query_text"],
            job_id="test_job_001",
            analysis_name="Test Analysis",
            periods=test_data["periods"],
            kpi_type="standard_kpi",
            id_panel=1,
            axes=test_data["axes"],
            filters=test_data["filters"],
            username="test_user"
        )
        
        logger.info(f"✓ Data insertion test passed. Result ID: {result_id}")
        return result_id
        
    except Exception as e:
        logger.error(f"Data insertion test failed: {e}")
        return ""

def test_data_retrieval(result_store: ResultStore, result_id: str) -> bool:
    """Test data retrieval with the new schema."""
    try:
        logger.info("Testing data retrieval...")
        
        # Test summary retrieval
        summary = result_store.get_result_summary(result_id)
        if not summary:
            logger.error("Failed to retrieve result summary")
            return False
            
        logger.info(f"Summary: {summary}")
        
        # Test detailed data retrieval
        data = result_store.get_result_data(result_id)
        if not data:
            logger.error("Failed to retrieve result data")
            return False
            
        logger.info(f"Data structure: {data.keys()}")
        logger.info(f"Number of rows: {data['total_rows']}")
        logger.info(f"Periods: {data['periods']}")
        logger.info(f"Facts: {data['facts']}")
        
        # Verify data structure
        if 'data' in data and len(data['data']) > 0:
            sample_row = data['data'][0]
            logger.info(f"Sample row structure: {sample_row.keys()}")
            
            # Check that axes and facts are properly structured
            if 'axes' in sample_row and 'facts' in sample_row:
                logger.info(f"Sample axes: {sample_row['axes']}")
                logger.info(f"Sample facts: {sample_row['facts']}")
            else:
                logger.error("Missing axes or facts in sample row")
                return False
        
        logger.info("✓ Data retrieval test passed")
        return True
        
    except Exception as e:
        logger.error(f"Data retrieval test failed: {e}")
        return False

def test_export_functionality(result_store: ResultStore, result_id: str) -> bool:
    """Test export functionality with the new schema."""
    try:
        logger.info("Testing export functionality...")
        
        # Test Parquet export
        export_path = f"test_export_{result_id}.parquet"
        success = result_store.export_to_parquet(result_id, export_path)
        
        if success:
            logger.info(f"✓ Export test passed. File created: {export_path}")
            
            # Clean up test file
            if os.path.exists(export_path):
                os.remove(export_path)
                logger.info("Test export file cleaned up")
                
            return True
        else:
            logger.error("Export test failed")
            return False
            
    except Exception as e:
        logger.error(f"Export test failed: {e}")
        return False

def main():
    """Main test function."""
    logger.info("Starting streamlined schema tests...")
    
    try:
        # Initialize connection and result store
        # Note: This assumes ClickHouse is running and accessible
        connection = ClickHouseConnection()
        result_store = ResultStore(connection)
        
        # Create test data
        test_data = create_test_data()
        
        # Run tests
        tests_passed = 0
        total_tests = 4
        
        # Test 1: Schema creation
        if test_schema_creation(result_store):
            tests_passed += 1
            
        # Test 2: Data insertion
        result_id = test_data_insertion(result_store, test_data)
        if result_id:
            tests_passed += 1
            
            # Test 3: Data retrieval (only if insertion succeeded)
            if test_data_retrieval(result_store, result_id):
                tests_passed += 1
                
            # Test 4: Export functionality (only if retrieval succeeded)
            if test_export_functionality(result_store, result_id):
                tests_passed += 1
                
            # Clean up test data
            try:
                result_store.delete_result(result_id, "test_user")
                logger.info("Test data cleaned up")
            except Exception as e:
                logger.warning(f"Failed to clean up test data: {e}")
        
        # Report results
        logger.info(f"\nTest Results: {tests_passed}/{total_tests} tests passed")
        
        if tests_passed == total_tests:
            logger.info("🎉 All tests passed! The streamlined schema is working correctly.")
            return 0
        else:
            logger.error("❌ Some tests failed. Please check the implementation.")
            return 1
            
    except Exception as e:
        logger.error(f"Test setup failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
